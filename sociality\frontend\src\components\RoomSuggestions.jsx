import React from 'react';
import {
  Box,
  Flex,
  Text,
  useColorModeValue,
  <PERSON><PERSON>,
  Badge,
  Icon
} from '@chakra-ui/react';
import { FaGlobe, FaTelegram, FaDiscord } from 'react-icons/fa';

const RoomSuggestions = ({ 
  suggestions = [], 
  isLoading = false, 
  onSelectRoom, 
  selectedIndex = -1,
  searchText = ""
}) => {
  const bgColor = useColorModeValue("white", "#1a1a1a");
  const borderColor = useColorModeValue("rgba(0, 0, 0, 0.08)", "rgba(255, 255, 255, 0.08)");
  const hoverBgColor = useColorModeValue("gray.50", "#1e1e1e");
  const selectedBgColor = useColorModeValue("rgba(0, 204, 133, 0.1)", "rgba(0, 204, 133, 0.15)");
  const textColor = useColorModeValue("gray.800", "white");
  const mutedTextColor = useColorModeValue("gray.600", "gray.400");

  // Platform icon mapping
  const getPlatformIcon = (platform) => {
    switch (platform?.toLowerCase()) {
      case 'telegram':
        return FaTelegram;
      case 'discord':
        return FaDiscord;
      default:
        return FaGlobe;
    }
  };

  // Platform color mapping
  const getPlatformColor = (platform) => {
    switch (platform?.toLowerCase()) {
      case 'telegram':
        return '#0088cc';
      case 'discord':
        return '#5865f2';
      default:
        return '#00CC85';
    }
  };

  if (isLoading) {
    return (
      <Box
        position="absolute"
        top="100%"
        left={0}
        right={0}
        bg={bgColor}
        border="1px solid"
        borderColor={borderColor}
        borderRadius="md"
        boxShadow="lg"
        zIndex={1000}
        p={3}
      >
        <Flex align="center" justify="center">
          <Spinner size="sm" color="#00CC85" />
          <Text ml={2} fontSize="sm" color={mutedTextColor}>
            Loading room suggestions...
          </Text>
        </Flex>
      </Box>
    );
  }

  if (!suggestions.length) {
    return null;
  }

  // Filter suggestions based on search text if provided
  const filteredSuggestions = searchText 
    ? suggestions.filter(room => 
        room.name.toLowerCase().includes(searchText.toLowerCase()) ||
        room.roomId.toLowerCase().includes(searchText.toLowerCase())
      )
    : suggestions;

  if (!filteredSuggestions.length) {
    return null;
  }

  return (
    <Box
      position="absolute"
      top="100%"
      left={0}
      right={0}
      bg={bgColor}
      border="1px solid"
      borderColor={borderColor}
      borderRadius="md"
      boxShadow="lg"
      zIndex={1000}
      maxH="200px"
      overflowY="auto"
    >
      {filteredSuggestions.map((room, index) => (
        <Flex
          key={room.roomId}
          align="center"
          p={3}
          cursor="pointer"
          bg={index === selectedIndex ? selectedBgColor : "transparent"}
          _hover={{ bg: index === selectedIndex ? selectedBgColor : hoverBgColor }}
          onClick={() => onSelectRoom(room)}
          borderBottom={index < filteredSuggestions.length - 1 ? "1px solid" : "none"}
          borderBottomColor={borderColor}
        >
          <Flex
            align="center"
            justify="center"
            w={8}
            h={8}
            bg="rgba(0, 204, 133, 0.1)"
            borderRadius="md"
            mr={3}
          >
            <Icon as={FaGlobe} color="#00CC85" size="16px" />
          </Flex>
          <Box flex={1}>
            <Flex align="center" mb={1}>
              <Text fontSize="sm" fontWeight="medium" color={textColor} mr={2}>
                {room.name}
              </Text>
              {room.peers && room.peers.length > 0 && (
                <Flex align="center" gap={1}>
                  {room.peers.slice(0, 3).map((peer, peerIndex) => (
                    <Icon
                      key={peerIndex}
                      as={getPlatformIcon(peer.platform)}
                      color={getPlatformColor(peer.platform)}
                      size="12px"
                    />
                  ))}
                  {room.peers.length > 3 && (
                    <Badge size="sm" colorScheme="gray" fontSize="xs">
                      +{room.peers.length - 3}
                    </Badge>
                  )}
                </Flex>
              )}
            </Flex>
            <Text fontSize="xs" color={mutedTextColor}>
              Room ID: {room.roomId}
            </Text>
          </Box>
        </Flex>
      ))}
    </Box>
  );
};

export default RoomSuggestions;
