import Room from "../models/roomModel.js";
import User from "../models/userModel.js";
import mongoose from "mongoose";
import logger from "../utils/logger.js";
import suggestionCache from "../utils/cache.js";

// Get intelligent room suggestions for cross-platform chat search
const getChatRoomSuggestions = async (req, res) => {
	try {
		const userId = req.user._id;

		if (!mongoose.Types.ObjectId.isValid(userId)) {
			return res.status(400).json({ error: "Invalid userId" });
		}

		// Check cache first
		const cachedSuggestions = suggestionCache.get('room_suggestions', userId);
		if (cachedSuggestions) {
			return res.status(200).json(cachedSuggestions);
		}

		// Get current user
		const currentUser = await User.findById(userId);
		if (!currentUser) {
			return res.status(404).json({ error: "User not found" });
		}

		// Get rooms where user is a participant
		const userRooms = await Room.find({
			'participants.user': userId,
			isActive: true
		})
		.select('roomId name description participants lastActivity messageCount federationSettings')
		.sort({ lastActivity: -1 })
		.limit(5);

		// Get popular federated rooms (most participants, recent activity)
		const popularRooms = await Room.find({
			'participants.user': { $ne: userId }, // Exclude rooms user is already in
			'federationSettings.isEnabled': true,
			isActive: true
		})
		.select('roomId name description participants lastActivity messageCount federationSettings')
		.sort({ 
			'participants.length': -1, // Most participants first
			lastActivity: -1 // Most recent activity
		})
		.limit(5);

		// Get recently active rooms (excluding user's rooms)
		const recentRooms = await Room.find({
			'participants.user': { $ne: userId },
			'federationSettings.isEnabled': true,
			isActive: true,
			lastActivity: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) } // Last 7 days
		})
		.select('roomId name description participants lastActivity messageCount federationSettings')
		.sort({ lastActivity: -1 })
		.limit(3);

		// Try to get federated rooms from federation registry
		let federatedRooms = [];
		try {
			const federationRegistryUrl = process.env.FEDERATION_REGISTRY_URL || 'http://localhost:7300';
			const response = await fetch(`${federationRegistryUrl}/federation/rooms`);
			if (response.ok) {
				const data = await response.json();
				federatedRooms = data.slice(0, 5); // Limit to 5 federated rooms
			}
		} catch (error) {
			logger.warn("Could not fetch federated rooms from registry:", error.message);
		}

		// Combine and prioritize suggestions
		const suggestions = [
			...userRooms.map(room => ({
				roomId: room.roomId,
				name: room.name,
				description: room.description,
				participantCount: room.participants.length,
				lastActivity: room.lastActivity,
				messageCount: room.messageCount,
				isParticipant: true,
				priority: 'user_rooms',
				peers: room.federationSettings?.registeredPeers || []
			})),
			...popularRooms.map(room => ({
				roomId: room.roomId,
				name: room.name,
				description: room.description,
				participantCount: room.participants.length,
				lastActivity: room.lastActivity,
				messageCount: room.messageCount,
				isParticipant: false,
				priority: 'popular',
				peers: room.federationSettings?.registeredPeers || []
			})),
			...recentRooms.map(room => ({
				roomId: room.roomId,
				name: room.name,
				description: room.description,
				participantCount: room.participants.length,
				lastActivity: room.lastActivity,
				messageCount: room.messageCount,
				isParticipant: false,
				priority: 'recent',
				peers: room.federationSettings?.registeredPeers || []
			})),
			...federatedRooms.map(room => ({
				roomId: room.roomId,
				name: room.name,
				description: room.description || '',
				participantCount: room.participantCount || 0,
				lastActivity: room.lastActivity || new Date(),
				messageCount: room.messageCount || 0,
				isParticipant: false,
				priority: 'federated',
				peers: room.peers || []
			}))
		];

		// Remove duplicates based on roomId
		const uniqueSuggestions = suggestions.filter((room, index, self) =>
			index === self.findIndex(r => r.roomId === room.roomId)
		);

		// Sort by priority and activity
		const priorityOrder = { 'user_rooms': 1, 'popular': 2, 'recent': 3, 'federated': 4 };
		const sortedSuggestions = uniqueSuggestions.sort((a, b) => {
			if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
				return priorityOrder[a.priority] - priorityOrder[b.priority];
			}
			return new Date(b.lastActivity) - new Date(a.lastActivity);
		});

		// Limit to 8 suggestions total
		const finalSuggestions = sortedSuggestions.slice(0, 8);

		// Cache the results
		suggestionCache.set('room_suggestions', userId, finalSuggestions);

		res.status(200).json(finalSuggestions);
	} catch (error) {
		logger.error("Error in getChatRoomSuggestions", error);
		res.status(500).json({ error: "Internal server error" });
	}
};

export {
	getChatRoomSuggestions,
};
