import Room from "../models/roomModel.js";
import User from "../models/userModel.js";
import mongoose from "mongoose";
import logger from "../utils/logger.js";
import suggestionCache from "../utils/cache.js";

// Get all room suggestions for cross-platform chat search from database
const getChatRoomSuggestions = async (req, res) => {
	try {
		const userId = req.user._id;
		const { query } = req.query; // Get search query from query parameters

		if (!mongoose.Types.ObjectId.isValid(userId)) {
			return res.status(400).json({ error: "Invalid userId" });
		}

		// Create cache key that includes search query
		const cacheKey = query ? `room_suggestions_${query}` : 'room_suggestions';
		const cachedSuggestions = suggestionCache.get(cacheKey, userId);
		if (cachedSuggestions) {
			return res.status(200).json(cachedSuggestions);
		}

		// Build search filter
		let searchFilter = {
			isActive: true // Only show active rooms
		};

		if (query && query.trim()) {
			searchFilter = {
				...searchFilter,
				$or: [
					{ name: { $regex: query.trim(), $options: "i" } },
					{ roomId: { $regex: query.trim(), $options: "i" } },
					{ description: { $regex: query.trim(), $options: "i" } }
				]
			};
		}

		// Get ALL rooms from database that match the criteria
		const allRooms = await Room.find(searchFilter)
			.select('roomId name description participants lastActivity messageCount federationSettings')
			.sort({
				name: 1, // Sort alphabetically by name
				lastActivity: -1 // Then by most recent activity
			});

		// Transform rooms into suggestion format
		let suggestions = [];
		if (query && query.trim()) {
			const queryLower = query.trim().toLowerCase();

			// Separate rooms into different priority groups
			const exactStartMatches = [];
			const containsMatches = [];

			allRooms.forEach(room => {
				const nameMatch = room.name.toLowerCase().startsWith(queryLower);
				const roomIdMatch = room.roomId.toLowerCase().startsWith(queryLower);

				if (nameMatch || roomIdMatch) {
					exactStartMatches.push(room);
				} else {
					containsMatches.push(room);
				}
			});

			// Combine with priority: exact matches first, then contains matches
			suggestions = [
				...exactStartMatches.map(room => ({
					roomId: room.roomId,
					name: room.name,
					description: room.description,
					participantCount: room.participants.length,
					lastActivity: room.lastActivity,
					messageCount: room.messageCount,
					isParticipant: room.participants.some(p => p.user.toString() === userId.toString()),
					priority: 'exact_match',
					peers: room.federationSettings?.registeredPeers || []
				})),
				...containsMatches.map(room => ({
					roomId: room.roomId,
					name: room.name,
					description: room.description,
					participantCount: room.participants.length,
					lastActivity: room.lastActivity,
					messageCount: room.messageCount,
					isParticipant: room.participants.some(p => p.user.toString() === userId.toString()),
					priority: 'contains_match',
					peers: room.federationSettings?.registeredPeers || []
				}))
			];
		} else {
			// When no search query, return all rooms sorted alphabetically
			suggestions = allRooms.map(room => ({
				roomId: room.roomId,
				name: room.name,
				description: room.description,
				participantCount: room.participants.length,
				lastActivity: room.lastActivity,
				messageCount: room.messageCount,
				isParticipant: room.participants.some(p => p.user.toString() === userId.toString()),
				priority: 'all_rooms',
				peers: room.federationSettings?.registeredPeers || []
			}));
		}

		// Cache the results
		suggestionCache.set(cacheKey, userId, suggestions);

		res.status(200).json(suggestions);
	} catch (error) {
		logger.error("Error in getChatRoomSuggestions", error);
		res.status(500).json({ error: "Internal server error" });
	}
};

export {
	getChatRoomSuggestions,
};
